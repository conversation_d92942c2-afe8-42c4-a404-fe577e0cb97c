"""
音频转换服务
负责将文本转换为音频文件，使用本地存储
"""

import os
import asyncio
import aiohttp
import json
from pathlib import Path
from typing import Optional, List, Dict, Any
import uuid
from datetime import datetime

from app.core.logging import get_logger
from app.core.config import settings
from app.services.storage_service import storage_service
from app.services.text_extraction_service import text_extraction_service

logger = get_logger(__name__)


class AudioConversionService:
    """音频转换服务"""
    
    def __init__(self):
        self.max_segment_length = settings.max_segment_length
        self.tts_api_url = settings.tts_api_url
        self.tts_api_key = settings.tts_api_key
        self.tts_model = settings.tts_model
        self.output_format = settings.tts_output_format
    
    async def convert_file_to_audio(
        self, 
        task_id: int, 
        user_id: int, 
        file_path: str, 
        file_type: str,
        voice_settings: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        将文件转换为音频
        
        Args:
            task_id: 任务ID
            user_id: 用户ID
            file_path: 文件路径
            file_type: 文件类型
            voice_settings: 语音设置
            
        Returns:
            Dict: 转换结果
        """
        try:
            logger.info(f"开始音频转换任务: {task_id}, 文件: {file_path}")
            
            # 1. 提取文本
            text_data = await text_extraction_service.extract_text_from_file(
                file_path, file_type
            )
            
            if not text_data.get("content"):
                raise ValueError("文件中没有提取到有效文本")
            
            # 2. 分割文本为段落
            segments = self._split_text_into_segments(text_data["content"])
            
            if not segments:
                raise ValueError("文本分割失败")
            
            logger.info(f"文本分割完成，共 {len(segments)} 个段落")
            
            # 3. 转换每个段落为音频
            audio_files = []
            total_segments = len(segments)
            
            for i, segment in enumerate(segments):
                try:
                    logger.info(f"转换段落 {i + 1}/{total_segments}")
                    
                    # 生成音频文件
                    audio_file = await self._convert_text_to_audio(
                        segment, 
                        task_id, 
                        user_id, 
                        i + 1,
                        voice_settings
                    )
                    
                    if audio_file:
                        audio_files.append(audio_file)
                    
                    # 计算进度（文本转换占80%）
                    progress = int((i + 1) / total_segments * 80)
                    yield {"type": "progress", "progress": progress, "message": f"转换段落 {i + 1}/{total_segments}"}
                    
                except Exception as e:
                    logger.error(f"段落 {i + 1} 转换失败: {str(e)}")
                    # 继续处理其他段落
                    continue
            
            if not audio_files:
                raise ValueError("所有音频段落转换失败")
            
            # 4. 生成播放列表
            playlist_data = self._generate_playlist(audio_files, text_data)
            
            # 5. 保存播放列表到本地存储
            playlist_path = await self._save_playlist(
                playlist_data, task_id, user_id
            )
            
            yield {"type": "progress", "progress": 90, "message": "生成播放列表"}
            
            # 6. 计算总时长（估算）
            total_duration = len(text_data["content"]) // 10  # 估算：每10个字符1秒
            
            result = {
                "audio_files": audio_files,
                "playlist_url": playlist_path,
                "total_duration": total_duration,
                "word_count": text_data["word_count"],
                "segment_count": len(audio_files),
                "title": text_data["title"]
            }
            
            yield {"type": "complete", "progress": 100, "message": "转换完成", "result": result}
            
            logger.info(f"音频转换完成: 任务 {task_id}, 生成 {len(audio_files)} 个音频文件")
            return result
            
        except Exception as e:
            logger.error(f"音频转换失败: {str(e)}")
            yield {"type": "error", "message": str(e)}
            raise
    
    def _split_text_into_segments(self, text: str) -> List[str]:
        """将文本分割为适合TTS的段落"""
        if not text:
            return []
        
        # 按句号、问号、感叹号分割
        sentences = []
        current_segment = ""
        
        for char in text:
            current_segment += char
            
            # 检查是否到达句子结尾
            if char in '。！？.!?' and len(current_segment.strip()) > 10:
                sentences.append(current_segment.strip())
                current_segment = ""
            
            # 检查段落长度限制
            elif len(current_segment) > self.max_segment_length:
                # 在最近的标点符号处分割
                last_punct = max(
                    current_segment.rfind('。'),
                    current_segment.rfind('！'),
                    current_segment.rfind('？'),
                    current_segment.rfind(','),
                    current_segment.rfind('，')
                )
                
                if last_punct > 0:
                    sentences.append(current_segment[:last_punct + 1].strip())
                    current_segment = current_segment[last_punct + 1:]
                else:
                    # 强制分割
                    sentences.append(current_segment.strip())
                    current_segment = ""
        
        # 添加剩余文本
        if current_segment.strip():
            sentences.append(current_segment.strip())
        
        # 过滤空段落
        return [s for s in sentences if s.strip() and len(s.strip()) > 5]
    
    async def _convert_text_to_audio(
        self, 
        text: str, 
        task_id: int, 
        user_id: int, 
        segment_index: int,
        voice_settings: Optional[Dict] = None
    ) -> Optional[Dict[str, Any]]:
        """将单个文本段落转换为音频"""
        try:
            # 准备TTS请求
            tts_data = {
                "model": voice_settings.get("model", self.tts_model) if voice_settings else self.tts_model,
                "input": text,
                "voice": voice_settings.get("voice", "alloy") if voice_settings else "alloy",
                "response_format": "mp3",
                "speed": voice_settings.get("speed", 1.0) if voice_settings else 1.0
            }
            
            headers = {
                "Authorization": f"Bearer {self.tts_api_key}",
                "Content-Type": "application/json"
            }
            
            # 调用TTS API
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.tts_api_url,
                    json=tts_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"TTS API调用失败: {response.status}, {error_text}")
                        return None
                    
                    # 读取音频数据
                    audio_data = await response.read()
            
            # 生成文件名
            filename = f"segment_{segment_index:04d}.mp3"
            
            # 保存到临时文件
            temp_path = Path(settings.temp_dir) / f"audio_{uuid.uuid4()}_{filename}"
            temp_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(temp_path, 'wb') as f:
                f.write(audio_data)
            
            # 上传到存储服务
            storage_path = storage_service.generate_audio_storage_path(
                str(user_id), str(task_id), filename
            )
            
            file_url = await storage_service.upload_file(
                str(temp_path), storage_path, "audio/mpeg"
            )
            
            # 清理临时文件
            try:
                temp_path.unlink()
            except:
                pass
            
            if not file_url:
                logger.error(f"音频文件上传失败: {filename}")
                return None
            
            return {
                "filename": filename,
                "url": file_url,
                "text": text[:100] + "..." if len(text) > 100 else text,
                "duration": len(text) // 10,  # 估算时长
                "size": len(audio_data),
                "segment_index": segment_index
            }
            
        except Exception as e:
            logger.error(f"文本转音频失败: {str(e)}")
            return None
    
    def _generate_playlist(self, audio_files: List[Dict], text_data: Dict) -> Dict[str, Any]:
        """生成播放列表"""
        return {
            "title": text_data["title"],
            "created_at": datetime.now().isoformat(),
            "total_segments": len(audio_files),
            "total_duration": sum(f.get("duration", 0) for f in audio_files),
            "segments": audio_files
        }
    
    async def _save_playlist(self, playlist_data: Dict, task_id: int, user_id: int) -> str:
        """保存播放列表到本地存储"""
        try:
            # 生成播放列表文件名
            filename = f"playlist_{task_id}.json"
            
            # 保存到临时文件
            temp_path = Path(settings.temp_dir) / f"playlist_{uuid.uuid4()}.json"
            temp_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(playlist_data, f, ensure_ascii=False, indent=2)
            
            # 上传到存储服务
            storage_path = storage_service.generate_audio_storage_path(
                str(user_id), str(task_id), filename
            )
            
            file_url = await storage_service.upload_file(
                str(temp_path), storage_path, "application/json"
            )
            
            # 清理临时文件
            try:
                temp_path.unlink()
            except:
                pass
            
            return file_url
            
        except Exception as e:
            logger.error(f"保存播放列表失败: {str(e)}")
            raise


# 创建全局音频转换服务实例
audio_conversion_service = AudioConversionService()
